using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CompanyName.Controls;

/// <summary>
/// A modern, professional tooltip control that extends WPF's standard tooltip functionality
/// with enhanced styling, animations, and content areas for title, content, icon, and footer.
/// </summary>
[TemplatePart(Name = PART_TitlePresenter, Type = typeof(ContentPresenter))]
[TemplatePart(Name = PART_ContentPresenter, Type = typeof(ContentPresenter))]
[TemplatePart(Name = PART_IconPresenter, Type = typeof(ContentPresenter))]
[TemplatePart(Name = PART_FooterPresenter, Type = typeof(ContentPresenter))]
[TemplatePart(Name = PART_MainBorder, Type = typeof(Border))]
public class ModernTooltip : ToolTip
{
    #region Template Parts
    private const string PART_TitlePresenter = "PART_TitlePresenter";
    private const string PART_ContentPresenter = "PART_ContentPresenter";
    private const string PART_IconPresenter = "PART_IconPresenter";
    private const string PART_FooterPresenter = "PART_FooterPresenter";
    private const string PART_MainBorder = "PART_MainBorder";

    private ContentPresenter? _titlePresenter;
    private ContentPresenter? _contentPresenter;
    private ContentPresenter? _iconPresenter;
    private ContentPresenter? _footerPresenter;
    private Border? _mainBorder;
    #endregion

    #region Dependency Properties

    /// <summary>
    /// Gets or sets the title text displayed at the top of the tooltip
    /// </summary>
    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    public static readonly DependencyProperty TitleProperty =
        DependencyProperty.Register(nameof(Title), typeof(string), typeof(ModernTooltip),
            new PropertyMetadata(string.Empty, OnTitleChanged));

    /// <summary>
    /// Gets or sets the main content of the tooltip
    /// </summary>
    public object TooltipContent
    {
        get => GetValue(TooltipContentProperty);
        set => SetValue(TooltipContentProperty, value);
    }

    public static readonly DependencyProperty TooltipContentProperty =
        DependencyProperty.Register(nameof(TooltipContent), typeof(object), typeof(ModernTooltip),
            new PropertyMetadata(null, OnTooltipContentChanged));

    /// <summary>
    /// Gets or sets the icon displayed in the tooltip
    /// </summary>
    public object Icon
    {
        get => GetValue(IconProperty);
        set => SetValue(IconProperty, value);
    }

    public static readonly DependencyProperty IconProperty =
        DependencyProperty.Register(nameof(Icon), typeof(object), typeof(ModernTooltip),
            new PropertyMetadata(null, OnIconChanged));

    /// <summary>
    /// Gets or sets the image source for the tooltip
    /// </summary>
    public ImageSource? Image
    {
        get => (ImageSource?)GetValue(ImageProperty);
        set => SetValue(ImageProperty, value);
    }

    public static readonly DependencyProperty ImageProperty =
        DependencyProperty.Register(nameof(Image), typeof(ImageSource), typeof(ModernTooltip),
            new PropertyMetadata(null, OnImageChanged));

    /// <summary>
    /// Gets or sets the footer content displayed at the bottom of the tooltip
    /// </summary>
    public object Footer
    {
        get => GetValue(FooterProperty);
        set => SetValue(FooterProperty, value);
    }

    public static readonly DependencyProperty FooterProperty =
        DependencyProperty.Register(nameof(Footer), typeof(object), typeof(ModernTooltip),
            new PropertyMetadata(null, OnFooterChanged));

    /// <summary>
    /// Gets or sets the duration in milliseconds for which the tooltip is displayed
    /// </summary>
    public int ShowDuration
    {
        get => (int)GetValue(ShowDurationProperty);
        set => SetValue(ShowDurationProperty, value);
    }

    public static readonly DependencyProperty ShowDurationProperty =
        DependencyProperty.Register(nameof(ShowDuration), typeof(int), typeof(ModernTooltip),
            new PropertyMetadata(5000, OnShowDurationChanged));

    /// <summary>
    /// Gets or sets whether animations are enabled for the tooltip
    /// </summary>
    public bool EnableAnimations
    {
        get => (bool)GetValue(EnableAnimationsProperty);
        set => SetValue(EnableAnimationsProperty, value);
    }

    public static readonly DependencyProperty EnableAnimationsProperty =
        DependencyProperty.Register(nameof(EnableAnimations), typeof(bool), typeof(ModernTooltip),
            new PropertyMetadata(true));

    /// <summary>
    /// Gets or sets the corner radius for the tooltip border
    /// </summary>
    public CornerRadius CornerRadius
    {
        get => (CornerRadius)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    public static readonly DependencyProperty CornerRadiusProperty =
        DependencyProperty.Register(nameof(CornerRadius), typeof(CornerRadius), typeof(ModernTooltip),
            new PropertyMetadata(new CornerRadius(4)));

    /// <summary>
    /// Gets or sets whether the tooltip is open/visible
    /// </summary>
    public bool IsTooltipOpen
    {
        get => (bool)GetValue(IsTooltipOpenProperty);
        set => SetValue(IsTooltipOpenProperty, value);
    }

    public static readonly DependencyProperty IsTooltipOpenProperty =
        DependencyProperty.Register(nameof(IsTooltipOpen), typeof(bool), typeof(ModernTooltip),
            new PropertyMetadata(false, OnIsTooltipOpenChanged));

    #endregion

    #region Constructor and Initialization

    static ModernTooltip()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(ModernTooltip),
            new FrameworkPropertyMetadata(typeof(ModernTooltip)));
    }

    public ModernTooltip()
    {
        Loaded += OnLoaded;
        Unloaded += OnUnloaded;
    }

    #endregion

    #region Template and Lifecycle

    public override void OnApplyTemplate()
    {
        base.OnApplyTemplate();

        _titlePresenter = GetTemplateChild(PART_TitlePresenter) as ContentPresenter;
        _contentPresenter = GetTemplateChild(PART_ContentPresenter) as ContentPresenter;
        _iconPresenter = GetTemplateChild(PART_IconPresenter) as ContentPresenter;
        _footerPresenter = GetTemplateChild(PART_FooterPresenter) as ContentPresenter;
        _mainBorder = GetTemplateChild(PART_MainBorder) as Border;

        UpdateVisibility();
    }

    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        if (EnableAnimations)
        {
            PlayOpenAnimation();
        }
    }

    private void OnUnloaded(object sender, RoutedEventArgs e)
    {
        if (EnableAnimations)
        {
            PlayCloseAnimation();
        }
    }

    #endregion

    #region Property Change Handlers

    private static void OnTitleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernTooltip tooltip)
        {
            tooltip.UpdateVisibility();
        }
    }

    private static void OnTooltipContentChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernTooltip tooltip)
        {
            tooltip.UpdateVisibility();
        }
    }

    private static void OnIconChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernTooltip tooltip)
        {
            tooltip.UpdateVisibility();
        }
    }

    private static void OnImageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernTooltip tooltip)
        {
            tooltip.UpdateVisibility();
        }
    }

    private static void OnFooterChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernTooltip tooltip)
        {
            tooltip.UpdateVisibility();
        }
    }

    private static void OnShowDurationChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernTooltip tooltip && e.NewValue is int duration)
        {
            tooltip.ShowDuration = Math.Max(0, duration);
        }
    }

    private static void OnIsTooltipOpenChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernTooltip tooltip && e.NewValue is bool isOpen)
        {
            tooltip.IsOpen = isOpen;
        }
    }

    #endregion

    #region Private Methods

    private void UpdateVisibility()
    {
        if (_titlePresenter != null)
        {
            _titlePresenter.Visibility = string.IsNullOrEmpty(Title) ? Visibility.Collapsed : Visibility.Visible;
        }

        if (_contentPresenter != null)
        {
            _contentPresenter.Visibility = TooltipContent == null ? Visibility.Collapsed : Visibility.Visible;
        }

        if (_iconPresenter != null)
        {
            _iconPresenter.Visibility = (Icon == null && Image == null) ? Visibility.Collapsed : Visibility.Visible;
        }

        if (_footerPresenter != null)
        {
            _footerPresenter.Visibility = Footer == null ? Visibility.Collapsed : Visibility.Visible;
        }
    }

    private void PlayOpenAnimation()
    {
        if (_mainBorder == null) return;

        var fadeIn = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(200))
        {
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        var scaleTransform = new ScaleTransform(0.95, 0.95);
        _mainBorder.RenderTransform = scaleTransform;
        _mainBorder.RenderTransformOrigin = new Point(0.5, 0.5);

        var scaleXAnimation = new DoubleAnimation(0.95, 1, TimeSpan.FromMilliseconds(200))
        {
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        var scaleYAnimation = new DoubleAnimation(0.95, 1, TimeSpan.FromMilliseconds(200))
        {
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        _mainBorder.BeginAnimation(OpacityProperty, fadeIn);
        scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
        scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
    }

    private void PlayCloseAnimation()
    {
        if (_mainBorder == null) return;

        var fadeOut = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(150))
        {
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
        };

        _mainBorder.BeginAnimation(OpacityProperty, fadeOut);
    }

    #endregion
}
