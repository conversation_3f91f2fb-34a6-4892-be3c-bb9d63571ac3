<mah:MetroWindow x:Class="ToolTipApp.Views.MainView"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
                 xmlns:controls="clr-namespace:CompanyName.Controls"
                 Title="{Binding DisplayName}"
                 Width="800" Height="600"
                 WindowStartupLocation="CenterScreen"
                 ResizeMode="CanResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="Modern Tooltip Control Demo"
                   Style="{DynamicResource MahApps.Styles.TextBlock.Title}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20" />

        <!-- Theme Selection -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <TextBlock Text="Theme:" VerticalAlignment="Center" Margin="0,0,10,0" />
            <ComboBox ItemsSource="{Binding AvailableThemes}"
                      SelectedItem="{Binding SelectedTheme}"
                      Width="120" />
        </StackPanel>

        <!-- Main Content Area -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- Simple Tooltip Examples -->
                <GroupBox Header="Simple Tooltips" Style="{DynamicResource MahApps.Styles.GroupBox}" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        
                        <!-- Basic Tooltip -->
                        <Button Content="Hover for Basic Tooltip"
                                Width="200" Height="40"
                                ToolTip="{Binding SimpleTooltipText}"
                                Margin="0,0,0,15" />

                        <!-- Custom Simple Tooltip -->
                        <Button Content="Custom Simple Tooltip"
                                Width="200" Height="40"
                                Margin="0,0,0,15">
                            <Button.ToolTip>
                                <controls:ModernTooltip TooltipContent="This is a custom styled tooltip using ModernTooltip control." />
                            </Button.ToolTip>
                        </Button>

                    </StackPanel>
                </GroupBox>

                <!-- Advanced Tooltip Examples -->
                <GroupBox Header="Advanced Tooltips" Style="{DynamicResource MahApps.Styles.GroupBox}" Margin="0,0,0,20">
                    <StackPanel Margin="10">

                        <!-- Rich Content Tooltip -->
                        <Button Content="Rich Content Tooltip"
                                Width="200" Height="40"
                                Margin="0,0,0,15">
                            <Button.ToolTip>
                                <controls:ModernTooltip Title="{Binding TooltipTitle}"
                                                      TooltipContent="{Binding TooltipContent}"
                                                      Footer="{Binding TooltipFooter}"
                                                      EnableAnimations="True">
                                    <controls:ModernTooltip.Icon>
                                        <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,17A1.5,1.5 0 0,1 10.5,15.5A1.5,1.5 0 0,1 12,14A1.5,1.5 0 0,1 13.5,15.5A1.5,1.5 0 0,1 12,17M12,10A1,1 0 0,1 13,11V15A1,1 0 0,1 12,16A1,1 0 0,1 11,15V11A1,1 0 0,1 12,10Z"
                                              Fill="DodgerBlue" Width="16" Height="16" />
                                    </controls:ModernTooltip.Icon>
                                </controls:ModernTooltip>
                            </Button.ToolTip>
                        </Button>

                        <!-- Themed Tooltips -->
                        <WrapPanel Margin="0,0,0,15">
                            <Button Content="Warning" Margin="5" Width="80" Height="30">
                                <Button.ToolTip>
                                    <controls:ModernTooltip Title="Warning"
                                                          TooltipContent="This is a warning message."
                                                          Background="#FFF3CD"
                                                          BorderBrush="#FFEAA7"
                                                          Foreground="#856404" />
                                </Button.ToolTip>
                            </Button>

                            <Button Content="Error" Margin="5" Width="80" Height="30">
                                <Button.ToolTip>
                                    <controls:ModernTooltip Title="Error"
                                                          TooltipContent="This is an error message."
                                                          Background="#F8D7DA"
                                                          BorderBrush="#F5C6CB"
                                                          Foreground="#721C24" />
                                </Button.ToolTip>
                            </Button>

                            <Button Content="Success" Margin="5" Width="80" Height="30">
                                <Button.ToolTip>
                                    <controls:ModernTooltip Title="Success"
                                                          TooltipContent="Operation completed successfully."
                                                          Background="#D4EDDA"
                                                          BorderBrush="#C3E6CB"
                                                          Foreground="#155724" />
                                </Button.ToolTip>
                            </Button>

                            <Button Content="Info" Margin="5" Width="80" Height="30">
                                <Button.ToolTip>
                                    <controls:ModernTooltip Title="Information"
                                                          TooltipContent="This is an informational message."
                                                          Background="#D1ECF1"
                                                          BorderBrush="#BEE5EB"
                                                          Foreground="#0C5460" />
                                </Button.ToolTip>
                            </Button>
                        </WrapPanel>

                        <!-- Theme-Aware Tooltip -->
                        <Button Content="Theme-Aware Tooltip"
                                Width="200" Height="40"
                                Margin="0,0,0,15">
                            <Button.ToolTip>
                                <controls:ModernTooltip Title="{Binding SelectedTheme, StringFormat='Current Theme: {0}'}"
                                                      TooltipContent="This tooltip changes appearance based on the selected theme."
                                                      Footer="Select different themes above to see changes">
                                    <controls:ModernTooltip.Style>
                                        <Style TargetType="{x:Type controls:ModernTooltip}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SelectedTheme}" Value="Dark">
                                                    <Setter Property="Background" Value="#2D2D30" />
                                                    <Setter Property="Foreground" Value="#F1F1F1" />
                                                    <Setter Property="BorderBrush" Value="#3F3F46" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SelectedTheme}" Value="Warning">
                                                    <Setter Property="Background" Value="#FFF3CD" />
                                                    <Setter Property="Foreground" Value="#856404" />
                                                    <Setter Property="BorderBrush" Value="#FFEAA7" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SelectedTheme}" Value="Error">
                                                    <Setter Property="Background" Value="#F8D7DA" />
                                                    <Setter Property="Foreground" Value="#721C24" />
                                                    <Setter Property="BorderBrush" Value="#F5C6CB" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SelectedTheme}" Value="Success">
                                                    <Setter Property="Background" Value="#D4EDDA" />
                                                    <Setter Property="Foreground" Value="#155724" />
                                                    <Setter Property="BorderBrush" Value="#C3E6CB" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SelectedTheme}" Value="Info">
                                                    <Setter Property="Background" Value="#D1ECF1" />
                                                    <Setter Property="Foreground" Value="#0C5460" />
                                                    <Setter Property="BorderBrush" Value="#BEE5EB" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </controls:ModernTooltip.Style>
                                </controls:ModernTooltip>
                            </Button.ToolTip>
                        </Button>

                    </StackPanel>
                </GroupBox>

                <!-- Interactive Examples -->
                <GroupBox Header="Interactive Examples" Style="{DynamicResource MahApps.Styles.GroupBox}" Margin="0,0,0,20">
                    <StackPanel Margin="10">

                        <!-- Programmatic Tooltip Control -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Button Content="Show Tooltip"
                                    Click="ShowTooltip_Click"
                                    Width="120" Height="35"
                                    Margin="0,0,10,0" />

                            <Button Content="Hide Tooltip"
                                    Click="HideTooltip_Click"
                                    Width="120" Height="35"
                                    Margin="0,0,10,0" />

                            <Button Content="Change Content"
                                    Click="ChangeContent_Click"
                                    Width="120" Height="35" />
                        </StackPanel>

                        <!-- Dynamic Content Display -->
                        <Grid>
                            <Border x:Name="TooltipTargetBorder"
                                    Background="{DynamicResource MahApps.Brushes.ThemeBackground}"
                                    BorderBrush="{DynamicResource MahApps.Brushes.ThemeForeground}"
                                    BorderThickness="1" Padding="10" CornerRadius="4">
                                <TextBlock Text="Hover here for dynamic tooltip"
                                           HorizontalAlignment="Center">
                                    <TextBlock.ToolTip>
                                        <controls:ModernTooltip Title="Dynamic Content"
                                                              TooltipContent="{Binding TooltipContent}"
                                                              Footer="Content updates in real-time" />
                                    </TextBlock.ToolTip>
                                </TextBlock>
                            </Border>

                            <!-- Programmatic Tooltip Popup -->
                            <Popup x:Name="ProgrammaticTooltipPopup"
                                   IsOpen="{Binding IsTooltipVisible}"
                                   Placement="Top"
                                   PlacementTarget="{Binding ElementName=TooltipTargetBorder}"
                                   AllowsTransparency="True"
                                   PopupAnimation="Fade">
                                <controls:ModernTooltip Title="Programmatic Tooltip"
                                                      TooltipContent="{Binding TooltipContent}"
                                                      Footer="Controlled via buttons"
                                                      Background="#FF2D2D30"
                                                      Foreground="White"
                                                      BorderBrush="#FF007ACC"
                                                      BorderThickness="1" />
                            </Popup>
                        </Grid>

                    </StackPanel>
                </GroupBox>

                <!-- Demo Items List -->
                <GroupBox Header="Demo Items with Rich Tooltips" Style="{DynamicResource MahApps.Styles.GroupBox}">
                    <ItemsControl ItemsSource="{Binding TooltipDemoItems}" Margin="10">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{DynamicResource MahApps.Brushes.ThemeBackground}"
                                        BorderBrush="{DynamicResource MahApps.Brushes.ThemeForeground}"
                                        BorderThickness="1" Padding="15" Margin="5"
                                        CornerRadius="4" Width="150" Height="100">
                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <Path Data="{Binding IconData}" 
                                              Fill="{Binding IconBrush}" 
                                              Width="24" Height="24" 
                                              HorizontalAlignment="Center" />
                                        <TextBlock Text="{Binding Title}" 
                                                   HorizontalAlignment="Center" 
                                                   Margin="0,5,0,0"
                                                   FontWeight="SemiBold" />
                                    </StackPanel>
                                    
                                    <Border.ToolTip>
                                        <controls:ModernTooltip Title="{Binding Title}"
                                                              TooltipContent="{Binding Content}"
                                                              Footer="{Binding Footer}"
                                                              EnableAnimations="True">
                                            <controls:ModernTooltip.Icon>
                                                <Path Data="{Binding IconData}" 
                                                      Fill="{Binding IconBrush}" 
                                                      Width="16" Height="16" />
                                            </controls:ModernTooltip.Icon>
                                        </controls:ModernTooltip>
                                    </Border.ToolTip>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <TextBlock Grid.Row="3" 
                   Text="Modern Tooltip Control - Professional WPF Component with MahApps.Metro Integration"
                   Style="{DynamicResource MahApps.Styles.TextBlock.Caption}"
                   HorizontalAlignment="Center"
                   Margin="0,20,0,0" />

    </Grid>
</mah:MetroWindow>
