using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using Caliburn.Micro;
using MahApps.Metro;

namespace ToolTipApp.ViewModels;

/// <summary>
/// Main ViewModel demonstrating the usage of ModernTooltip with Caliburn.Micro
/// </summary>
public class MainViewModel : Screen
{
    #region Fields
    private string _simpleTooltipText = "This is a simple tooltip example";
    private string _tooltipTitle = "Advanced Tooltip";
    private string _tooltipContent = "This is an advanced tooltip with multiple content areas including title, content, icon, and footer sections.";
    private string _tooltipFooter = "Press F1 for more help";
    private bool _isTooltipVisible;
    private string _selectedTheme = "Light";
    #endregion

    #region Properties

    /// <summary>
    /// Gets or sets the simple tooltip text
    /// </summary>
    public string SimpleTooltipText
    {
        get => _simpleTooltipText;
        set
        {
            _simpleTooltipText = value;
            NotifyOfPropertyChange();
        }
    }

    /// <summary>
    /// Gets or sets the tooltip title
    /// </summary>
    public string TooltipTitle
    {
        get => _tooltipTitle;
        set
        {
            _tooltipTitle = value;
            NotifyOfPropertyChange();
        }
    }

    /// <summary>
    /// Gets or sets the tooltip content
    /// </summary>
    public string TooltipContent
    {
        get => _tooltipContent;
        set
        {
            _tooltipContent = value;
            NotifyOfPropertyChange();
        }
    }

    /// <summary>
    /// Gets or sets the tooltip footer
    /// </summary>
    public string TooltipFooter
    {
        get => _tooltipFooter;
        set
        {
            _tooltipFooter = value;
            NotifyOfPropertyChange();
        }
    }

    /// <summary>
    /// Gets or sets whether the tooltip is visible
    /// </summary>
    public bool IsTooltipVisible
    {
        get => _isTooltipVisible;
        set
        {
            _isTooltipVisible = value;
            NotifyOfPropertyChange();
        }
    }

    /// <summary>
    /// Gets or sets the selected theme
    /// </summary>
    public string SelectedTheme
    {
        get => _selectedTheme;
        set
        {
            _selectedTheme = value;
            NotifyOfPropertyChange();
            NotifyOfPropertyChange(nameof(CurrentTooltipStyle));
            ApplyTheme(value);
        }
    }

    /// <summary>
    /// Gets the available themes
    /// </summary>
    public ObservableCollection<string> AvailableThemes { get; } = new()
    {
        "Light",
        "Dark", 
        "Accent",
        "Warning",
        "Error",
        "Success",
        "Info"
    };

    /// <summary>
    /// Gets the current tooltip style based on selected theme
    /// </summary>
    public string CurrentTooltipStyle => $"ModernTooltip{SelectedTheme}Style";

    /// <summary>
    /// Gets the background brush for the current theme
    /// </summary>
    public Brush ThemeBackgroundBrush => new SolidColorBrush((Color)ColorConverter.ConvertFromString(SelectedTheme.ToLower() switch
    {
        "dark" => "#2D2D30",
        "warning" => "#FFF3CD",
        "error" => "#F8D7DA",
        "success" => "#D4EDDA",
        "info" => "#D1ECF1",
        "accent" => "#007ACC",
        _ => "#FFFFFF" // Light theme
    }));

    /// <summary>
    /// Gets the foreground brush for the current theme
    /// </summary>
    public Brush ThemeForegroundBrush => new SolidColorBrush((Color)ColorConverter.ConvertFromString(SelectedTheme.ToLower() switch
    {
        "dark" => "#F1F1F1",
        "warning" => "#856404",
        "error" => "#721C24",
        "success" => "#155724",
        "info" => "#0C5460",
        "accent" => "#FFFFFF",
        _ => "#333333" // Light theme
    }));

    /// <summary>
    /// Gets the border brush for the current theme
    /// </summary>
    public Brush ThemeBorderBrush => new SolidColorBrush((Color)ColorConverter.ConvertFromString(SelectedTheme.ToLower() switch
    {
        "dark" => "#3F3F46",
        "warning" => "#FFEAA7",
        "error" => "#F5C6CB",
        "success" => "#C3E6CB",
        "info" => "#BEE5EB",
        "accent" => "#005A9E",
        _ => "#E0E0E0" // Light theme
    }));

    /// <summary>
    /// Sample tooltip data for demonstration
    /// </summary>
    public ObservableCollection<TooltipDemoItem> TooltipDemoItems { get; } = new();

    #endregion

    #region Constructor

    public MainViewModel()
    {
        DisplayName = "Modern Tooltip Demo";
        InitializeDemoItems();
    }

    #endregion

    #region Commands

    /// <summary>
    /// Command to show a programmatic tooltip
    /// </summary>
    public void ShowProgrammaticTooltip()
    {
        IsTooltipVisible = true;
        
        // Auto-hide after 3 seconds
        Task.Delay(3000).ContinueWith(_ =>
        {
            Execute.OnUIThread(() => IsTooltipVisible = false);
        });
    }

    /// <summary>
    /// Command to hide the programmatic tooltip
    /// </summary>
    public void HideProgrammaticTooltip()
    {
        IsTooltipVisible = false;
    }

    /// <summary>
    /// Command to change tooltip content dynamically
    /// </summary>
    public void ChangeTooltipContent()
    {
        var random = new Random();
        var contents = new[]
        {
            "Dynamic content updated!",
            "This content was changed programmatically.",
            "Tooltips can be updated in real-time.",
            "MVVM pattern makes this easy!"
        };

        TooltipContent = contents[random.Next(contents.Length)];
    }

    /// <summary>
    /// Command to demonstrate tooltip with command binding
    /// </summary>
    public void ExecuteTooltipAction()
    {
        MessageBox.Show("Tooltip action executed!", "Action", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    /// <summary>
    /// Determines if the tooltip action can be executed
    /// </summary>
    public bool CanExecuteTooltipAction => !string.IsNullOrEmpty(TooltipContent);

    #endregion

    #region Private Methods

    private void ApplyTheme(string themeName)
    {
        try
        {
            var app = Application.Current;
            if (app?.Resources == null) return;

            // Clear existing theme resources
            var resourcesToRemove = app.Resources.MergedDictionaries
                .Where(d => d.Source?.ToString().Contains("Themes") == true)
                .ToList();

            foreach (var resource in resourcesToRemove)
            {
                app.Resources.MergedDictionaries.Remove(resource);
            }

            // Apply new theme
            string themeUri = themeName.ToLower() switch
            {
                "dark" => "pack://application:,,,/MahApps.Metro;component/Styles/Themes/Dark.Blue.xaml",
                "light" => "pack://application:,,,/MahApps.Metro;component/Styles/Themes/Light.Blue.xaml",
                _ => "pack://application:,,,/MahApps.Metro;component/Styles/Themes/Light.Blue.xaml"
            };

            var themeDict = new ResourceDictionary { Source = new Uri(themeUri) };
            app.Resources.MergedDictionaries.Add(themeDict);

            // Update tooltip colors based on theme
            UpdateTooltipColors(themeName);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error applying theme: {ex.Message}");
        }
    }

    private void UpdateTooltipColors(string themeName)
    {
        // This will trigger property change notifications for any bound tooltip colors
        NotifyOfPropertyChange(nameof(SelectedTheme));
        NotifyOfPropertyChange(nameof(ThemeBackgroundBrush));
        NotifyOfPropertyChange(nameof(ThemeForegroundBrush));
        NotifyOfPropertyChange(nameof(ThemeBorderBrush));
    }

    private void InitializeDemoItems()
    {
        TooltipDemoItems.Add(new TooltipDemoItem
        {
            Title = "File Operations",
            Content = "Create, open, save, and manage your files with ease.",
            IconData = "M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z",
            IconBrush = Brushes.Blue,
            Footer = "Ctrl+N for new file"
        });

        TooltipDemoItems.Add(new TooltipDemoItem
        {
            Title = "Edit Functions",
            Content = "Cut, copy, paste, and undo operations for efficient editing.",
            IconData = "M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z",
            IconBrush = Brushes.Green,
            Footer = "Ctrl+Z to undo"
        });

        TooltipDemoItems.Add(new TooltipDemoItem
        {
            Title = "Settings",
            Content = "Configure application preferences and customize your experience.",
            IconData = "M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z",
            IconBrush = Brushes.Orange,
            Footer = "F12 to open settings"
        });
    }

    #endregion
}

/// <summary>
/// Demo item for tooltip examples
/// </summary>
public class TooltipDemoItem : PropertyChangedBase
{
    private string _title = string.Empty;
    private string _content = string.Empty;
    private string _iconData = string.Empty;
    private Brush _iconBrush = Brushes.Black;
    private string _footer = string.Empty;

    public string Title
    {
        get => _title;
        set
        {
            _title = value;
            NotifyOfPropertyChange();
        }
    }

    public string Content
    {
        get => _content;
        set
        {
            _content = value;
            NotifyOfPropertyChange();
        }
    }

    public string IconData
    {
        get => _iconData;
        set
        {
            _iconData = value;
            NotifyOfPropertyChange();
        }
    }

    public Brush IconBrush
    {
        get => _iconBrush;
        set
        {
            _iconBrush = value;
            NotifyOfPropertyChange();
        }
    }

    public string Footer
    {
        get => _footer;
        set
        {
            _footer = value;
            NotifyOfPropertyChange();
        }
    }
}
