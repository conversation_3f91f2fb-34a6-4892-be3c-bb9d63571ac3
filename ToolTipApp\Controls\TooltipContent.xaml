<UserControl x:Class="CompanyName.Controls.TooltipContent"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="clr-namespace:ToolTipApp.Converters">

    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
        <converters:MathConverter x:Key="MathConverter" />
    </UserControl.Resources>

    <Border Background="{Binding Background, RelativeSource={RelativeSource AncestorType=UserControl}}"
            BorderBrush="{Binding BorderBrush, RelativeSource={RelativeSource AncestorType=UserControl}}"
            BorderThickness="{Binding BorderThickness, RelativeSource={RelativeSource AncestorType=UserControl}}"
            CornerRadius="{Binding CornerRadius, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Padding="{Binding Padding, RelativeSource={RelativeSource AncestorType=UserControl}}"
            MaxWidth="400">
        
        <Border.Effect>
            <DropShadowEffect Color="#000000" Opacity="0.15" BlurRadius="8" ShadowDepth="2" Direction="270" />
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!-- Icon/Image Area -->
            <ContentPresenter x:Name="PART_IconPresenter"
                            Grid.Row="0"
                            Grid.Column="0"
                            Grid.RowSpan="2"
                            Margin="0,0,8,0"
                            VerticalAlignment="Top"
                            HorizontalAlignment="Left"
                            Content="{Binding Icon, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            Visibility="{Binding Icon, RelativeSource={RelativeSource AncestorType=UserControl}, 
                                       Converter={StaticResource NullToVisibilityConverter}}" />

            <!-- Title Area -->
            <ContentPresenter x:Name="PART_TitlePresenter"
                            Grid.Row="0"
                            Grid.Column="1"
                            Content="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            Margin="0,0,0,4"
                            VerticalAlignment="Top"
                            Visibility="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}, 
                                       Converter={StaticResource NullToVisibilityConverter}}">
                <ContentPresenter.Resources>
                    <Style TargetType="TextBlock">
                        <Setter Property="FontWeight" Value="SemiBold" />
                        <Setter Property="FontSize" Value="14" />
                        <Setter Property="Foreground" Value="{Binding Foreground, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                        <Setter Property="TextWrapping" Value="Wrap" />
                    </Style>
                </ContentPresenter.Resources>
            </ContentPresenter>

            <!-- Content Area -->
            <ContentPresenter x:Name="PART_ContentPresenter"
                            Grid.Row="1"
                            Grid.Column="1"
                            Content="{Binding TooltipText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            VerticalAlignment="Top"
                            Visibility="{Binding TooltipText, RelativeSource={RelativeSource AncestorType=UserControl},
                                       Converter={StaticResource NullToVisibilityConverter}}">
                <ContentPresenter.Resources>
                    <Style TargetType="TextBlock">
                        <Setter Property="TextWrapping" Value="Wrap" />
                        <Setter Property="Foreground" Value="{Binding Foreground, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                        <Setter Property="LineHeight" Value="18" />
                    </Style>
                </ContentPresenter.Resources>
            </ContentPresenter>

            <!-- Footer Area -->
            <ContentPresenter x:Name="PART_FooterPresenter"
                            Grid.Row="2"
                            Grid.Column="0"
                            Grid.ColumnSpan="2"
                            Content="{Binding Footer, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            Margin="0,8,0,0"
                            VerticalAlignment="Bottom"
                            Visibility="{Binding Footer, RelativeSource={RelativeSource AncestorType=UserControl}, 
                                       Converter={StaticResource NullToVisibilityConverter}}">
                <ContentPresenter.Resources>
                    <Style TargetType="TextBlock">
                        <Setter Property="FontSize" Value="11" />
                        <Setter Property="Foreground" Value="{Binding Foreground, RelativeSource={RelativeSource AncestorType=UserControl}}" />
                        <Setter Property="Opacity" Value="0.8" />
                        <Setter Property="TextWrapping" Value="Wrap" />
                    </Style>
                </ContentPresenter.Resources>
            </ContentPresenter>
        </Grid>
    </Border>

</UserControl>
