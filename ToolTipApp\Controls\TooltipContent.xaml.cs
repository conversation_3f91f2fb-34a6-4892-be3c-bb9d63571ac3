using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace CompanyName.Controls;

/// <summary>
/// A UserControl that displays tooltip content similar to ModernTooltip but can be used in Popups
/// </summary>
public partial class TooltipContent : UserControl
{
    #region Dependency Properties

    /// <summary>
    /// Gets or sets the title text displayed at the top of the tooltip
    /// </summary>
    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    public static readonly DependencyProperty TitleProperty =
        DependencyProperty.Register(nameof(Title), typeof(string), typeof(TooltipContent),
            new PropertyMetadata(string.Empty));

    /// <summary>
    /// Gets or sets the main content of the tooltip
    /// </summary>
    public object TooltipText
    {
        get => GetValue(TooltipTextProperty);
        set => SetValue(TooltipTextProperty, value);
    }

    public static readonly DependencyProperty TooltipTextProperty =
        DependencyProperty.Register(nameof(TooltipText), typeof(object), typeof(TooltipContent),
            new PropertyMetadata(null));

    /// <summary>
    /// Gets or sets the icon displayed in the tooltip
    /// </summary>
    public object Icon
    {
        get => GetValue(IconProperty);
        set => SetValue(IconProperty, value);
    }

    public static readonly DependencyProperty IconProperty =
        DependencyProperty.Register(nameof(Icon), typeof(object), typeof(TooltipContent),
            new PropertyMetadata(null));

    /// <summary>
    /// Gets or sets the footer content displayed at the bottom of the tooltip
    /// </summary>
    public object Footer
    {
        get => GetValue(FooterProperty);
        set => SetValue(FooterProperty, value);
    }

    public static readonly DependencyProperty FooterProperty =
        DependencyProperty.Register(nameof(Footer), typeof(object), typeof(TooltipContent),
            new PropertyMetadata(null));

    /// <summary>
    /// Gets or sets the corner radius for the tooltip border
    /// </summary>
    public CornerRadius CornerRadius
    {
        get => (CornerRadius)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    public static readonly DependencyProperty CornerRadiusProperty =
        DependencyProperty.Register(nameof(CornerRadius), typeof(CornerRadius), typeof(TooltipContent),
            new PropertyMetadata(new CornerRadius(4)));

    #endregion

    #region Constructor

    public TooltipContent()
    {
        InitializeComponent();
        
        // Set default values
        Background = new SolidColorBrush(Color.FromRgb(45, 45, 48)); // Dark background
        Foreground = new SolidColorBrush(Colors.White);
        BorderBrush = new SolidColorBrush(Color.FromRgb(0, 122, 204)); // Blue border
        BorderThickness = new Thickness(1);
        Padding = new Thickness(12, 8, 12, 8);
    }

    #endregion
}
